{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "^1.7.0", "@nuxt/image": "^1.10.0", "@nuxtjs/tailwindcss": "^6.14.0", "apexcharts": "^5.3.1", "eslint": "^9.31.0", "nuxt": "^4.0.1", "shadcn-nuxt": "^2.2.0", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0"}}